'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, Cable, Loader2 } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'

interface AggiungiCaviDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface CavoConMetri extends Cavo {
  metri_inseriti?: number
}

export default function AggiungiCaviDialogSimple({
  open,
  onClose,
  bobina,
  onSuccess,
  onError
}: AggiungiCaviDialogProps) {
  const { cantiere } = useAuth()
  const [loading, setLoading] = useState(false)
  const [caviLoading, setCaviLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  // Stati per i cavi
  const [caviCompatibili, setCaviCompatibili] = useState<CavoConMetri[]>([])
  const [caviIncompatibili, setCaviIncompatibili] = useState<CavoConMetri[]>([])
  const [caviSelezionati, setCaviSelezionati] = useState<CavoConMetri[]>([])
  const [caviMetri, setCaviMetri] = useState<Record<string, string>>({})
  
  // Stati per validazione
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Carica i cavi disponibili
  const loadCavi = async () => {
    if (!cantiere || !bobina) return

    try {
      setCaviLoading(true)
      console.log('🔍 Caricamento cavi per cantiere:', cantiere.id_cantiere)
      
      const caviData = await caviApi.getCavi(cantiere.id_cantiere)
      console.log('📦 Cavi ricevuti:', caviData.length)

      // Filtro semplice: solo cavi non installati
      const caviNonInstallati = caviData.filter(cavo => {
        const isInstalled = cavo.stato_installazione?.toLowerCase() === 'installato'
        const hasMeters = parseFloat(cavo.metratura_reale?.toString() || '0') > 0
        const isSpare = cavo.modificato_manualmente === 3
        const hasTheoreticalMeters = parseFloat(cavo.metri_teorici?.toString() || '0') > 0
        
        return !isInstalled && !hasMeters && !isSpare && hasTheoreticalMeters
      })

      console.log('📊 Cavi disponibili:', caviNonInstallati.length)

      // Compatibilità semplice
      const compatibili = caviNonInstallati.filter(cavo => 
        cavo.tipologia === bobina.tipologia && cavo.sezione === bobina.sezione
      )
      
      const incompatibili = caviNonInstallati.filter(cavo => 
        !(cavo.tipologia === bobina.tipologia && cavo.sezione === bobina.sezione)
      )

      console.log('✅ Compatibili:', compatibili.length)
      console.log('✅ Incompatibili:', incompatibili.length)

      setCaviCompatibili(compatibili)
      setCaviIncompatibili(incompatibili)
    } catch (error: any) {
      console.error('❌ Errore caricamento cavi:', error)
      onError('Errore nel caricamento dei cavi')
    } finally {
      setCaviLoading(false)
    }
  }

  // Reset quando si apre il dialog
  useEffect(() => {
    console.log('🔄 Dialog opened:', { open, bobina: !!bobina, cantiere: !!cantiere })
    if (open && bobina && cantiere) {
      console.log('📞 Loading cavi...')
      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      loadCavi()
    }
  }, [open, bobina, cantiere])

  // Gestisce la selezione di un cavo
  const handleCavoToggle = (cavo: CavoConMetri, isCompatible: boolean) => {
    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)

    if (isSelected) {
      // Rimuovi dalla selezione
      setCaviSelezionati(prev => prev.filter(c => c.id_cavo !== cavo.id_cavo))
      setCaviMetri(prev => {
        const newMetri = { ...prev }
        delete newMetri[cavo.id_cavo]
        return newMetri
      })
    } else {
      // Aggiungi alla selezione
      setCaviSelezionati(prev => [...prev, cavo])
      // Imposta metri teorici come default
      setCaviMetri(prev => ({
        ...prev,
        [cavo.id_cavo]: cavo.metri_teorici?.toString() || '0'
      }))
    }
  }

  // Gestisce il cambio dei metri
  const handleMetriChange = (cavoId: string, value: string) => {
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }))
  }

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!cantiere || !bobina) return

    try {
      setSaving(true)

      // Aggiorna ogni cavo selezionato
      const results = []
      const errors = []

      for (const cavo of caviSelezionati) {
        try {
          const metriPosati = parseFloat(caviMetri[cavo.id_cavo])

          await caviApi.updateMetriPosati(
            cantiere.id_cantiere,
            cavo.id_cavo,
            metriPosati,
            bobina.id_bobina
          )

          results.push({
            cavo: cavo.id_cavo,
            metriPosati,
            success: true
          })
        } catch (error: any) {
          console.error(`Errore aggiornamento cavo ${cavo.id_cavo}:`, error)
          errors.push({
            cavo: cavo.id_cavo,
            error: error.message || 'Errore sconosciuto'
          })
        }
      }

      // Gestione del risultato
      if (errors.length === 0) {
        onSuccess(`${results.length} cavi aggiornati con successo`)
        onClose()
      } else {
        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)
      }
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      onError('Errore durante il salvataggio dei cavi')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      onClose()
    }
  }

  if (!bobina) return null

  const getBobinaNumber = (idBobina: string) => {
    const match = idBobina.match(/C\d+_B(\d+)/)
    return match ? match[1] : idBobina
  }

  // Renderizza la lista dei cavi
  const renderCaviList = (cavi: CavoConMetri[], isCompatible: boolean) => {
    if (cavi.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Cable className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <div>Nessun cavo {isCompatible ? 'compatibile' : 'incompatibile'} disponibile</div>
          <div className="text-xs mt-2 text-gray-400">
            {isCompatible
              ? `Cerca cavi con tipologia "${bobina?.tipologia}" e formazione "${bobina?.sezione}"`
              : 'I cavi incompatibili hanno tipologia o formazione diverse'
            }
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {cavi.map(cavo => {
          const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)
          const metri = caviMetri[cavo.id_cavo] || ''

          return (
            <Card key={cavo.id_cavo} className={`p-3 ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
              <div className="flex items-start gap-3">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => handleCavoToggle(cavo, isCompatible)}
                  className="mt-1"
                />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">{cavo.id_cavo}</span>
                    <Badge variant="outline" className="text-xs">
                      {cavo.tipologia}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {cavo.sezione}
                    </Badge>
                    {!isCompatible && (
                      <Badge variant="destructive" className="text-xs">
                        Incompatibile
                      </Badge>
                    )}
                  </div>

                  <div className="text-xs text-gray-600 mb-2">
                    Metri teorici: {cavo.metri_teorici}m
                  </div>

                  {isSelected && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`metri-${cavo.id_cavo}`} className="text-xs">
                        Metri da posare:
                      </Label>
                      <Input
                        id={`metri-${cavo.id_cavo}`}
                        type="number"
                        step="0.1"
                        min="0"
                        value={metri}
                        onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                        className="w-24 h-8 text-xs"
                        placeholder="0"
                      />
                      <span className="text-xs text-gray-500">m</span>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Cable className="h-5 w-5" />
            Aggiungi cavi alla bobina {getBobinaNumber(bobina.id_bobina)}
          </DialogTitle>
          <DialogDescription>
            Seleziona i cavi da associare alla bobina e inserisci i metri posati
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informazioni bobina */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Dettagli bobina</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <Label className="text-xs text-gray-600">Bobina</Label>
                  <div className="font-medium">{getBobinaNumber(bobina.id_bobina)}</div>
                </div>
                <div>
                  <Label className="text-xs text-gray-600">Tipologia</Label>
                  <div className="font-medium">{bobina.tipologia}</div>
                </div>
                <div>
                  <Label className="text-xs text-gray-600">Formazione</Label>
                  <div className="font-medium">{bobina.sezione}</div>
                </div>
                <div>
                  <Label className="text-xs text-gray-600">Metri Residui</Label>
                  <div className="font-medium">{bobina.metri_residui}m</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Loading */}
          {caviLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Caricamento cavi...</span>
            </div>
          )}

          {/* Tabs per cavi compatibili/incompatibili */}
          {!caviLoading && (
            <Tabs defaultValue="compatibili" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="compatibili">
                  Cavi compatibili ({caviCompatibili.length})
                </TabsTrigger>
                <TabsTrigger value="incompatibili">
                  Cavi incompatibili ({caviIncompatibili.length})
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="compatibili" className="space-y-2">
                <div className="text-sm text-gray-600 mb-2">
                  Cavi compatibili con tipologia <strong>{bobina.tipologia}</strong> e formazione <strong>{bobina.sezione}</strong>
                </div>
                {renderCaviList(caviCompatibili, true)}
              </TabsContent>

              <TabsContent value="incompatibili" className="space-y-2">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    I cavi incompatibili possono essere utilizzati ma potrebbero non rispettare le specifiche tecniche della bobina.
                  </AlertDescription>
                </Alert>
                {renderCaviList(caviIncompatibili, false)}
              </TabsContent>
            </Tabs>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={saving}>
              Annulla
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving || caviSelezionati.length === 0}
            >
              {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {saving ? 'Salvataggio...' : `Salva ${caviSelezionati.length} cavi`}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
