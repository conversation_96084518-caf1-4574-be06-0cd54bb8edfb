{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "8d0b9685cbc76a83aeb3112e01c043ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ef83582421d329c20efe4ef597bfeca27be0afd780c644c5bdafba1c326a1a7d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "300d46f5bb8fdb41d67bb6d2cd870cf8b0557e286822f72bf685c67bbad58b73"}}}, "sortedMiddleware": ["/"], "functions": {}}